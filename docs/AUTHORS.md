# Authors

Marked takes an encompassing approach to its community. As such, you can think of these as [concentric circles](https://medium.com/the-node-js-collection/healthy-open-source-967fa8be7951), where each group encompasses the following groups.

<table>
  <tbody>
  	<tr>
      <td align="center" valign="top" style="width:32%">
        <a href="https://github.com/chjj">
          <img width="100" height="100" src="https://github.com/chjj.png?s=150">
        </a>
        <br>
        <a href="https://github.com/chjj"><PERSON></a>
        <div>Original Author</div>
        <small>Started the fire</small>
      </td>
      <td align="center" valign="top" style="width:32%">
        <a href="https://github.com/joshbruce">
          <img width="100" height="100" src="https://github.com/joshbruce.png?s=150">
        </a>
        <br>
        <a href="https://joshbruce.com"><PERSON></a>
        <div>Publisher</div>
        <small><PERSON> Wrangler; <PERSON><PERSON> Helper; <PERSON><PERSON>r of Hypertext</small>
      </td>
      <td align="center" valign="top" style="width:32%">
        <a href="https://github.com/styfle">
          <img width="100" height="100" src="https://github.com/styfle.png?s=150">
        </a>
        <br>
        <a href="https://www.ceriously.com">Steven</a>
        <div>Publisher</div>
        <small>Release Wrangler; Dr. Docs; Open source, of course; GitHub Guru; Humaning Helper</small>
      </td>
    </tr>
  	<tr>
      <td align="center" valign="top">
        <a href="https://github.com/davisjam">
          <img width="100" height="100" src="https://github.com/davisjam.png?s=150">
        </a>
        <br>
        <a href="https://github.com/davisjam">Jamie Davis</a>
        <div>Committer</div>
        <small>Seeker of Security</small>
      </td>
      <td align="center" valign="top">
        <a href="https://github.com/UziTech">
          <img width="100" height="100" src="https://github.com/UziTech.png?s=150">
        </a>
        <br>
        <a href="https://tony.brix.ninja">Tony Brix</a>
        <div>Publisher</div>
        <small>Release Wrangler; Titan of the test harness; Dr. DevOps</small>
      </td>
      <td align="center" valign="top">
        <a href="https://github.com/calculuschild">
          <img width="100" height="100" src="https://github.com/calculuschild.png?s=150">
        </a>
        <br>
        <a href="https://github.com/calculuschild">Trevor Buckner</a>
        <div>Committer</div>
        <small>Master of Marked</small>
      </td>
    </tr>
  </tbody>
</table>

## Contributors

<table>
  <tbody>
    <tr>
      <td align="center" valign="top">
        <a href="https://github.com/intcreator">
          <img width="100" height="100" src="https://github.com/intcreator.png?s=150">
        </a>
        <br>
        <a href="https://github.com/intcreator">Brandon der Blätter</a>
        <div>Contributor</div>
        <small>Curious Contributor</small>
      </td>
      <td align="center" valign="top">
        <a href="https://github.com/carlosvalle">
          <img width="100" height="100" src="https://github.com/carlosvalle.png?s=150">
        </a>
        <br>
        <a href="https://github.com/carlosvalle">Carlos Valle</a>
        <div>Contributor</div>
        <small>Maker of the Marked mark from 2018 to present</small>
      </td>
      <td align="center" width="20%" valign="top">
        <a href="https://github.com/Feder1co5oave">
          <img width="100" height="100" src="https://github.com/Feder1co5oave.png?s=150">
        </a>
        <br>
        <a href="https://github.com/Feder1co5oave">Federico Soave</a>
        <div>Contributor</div>
        <small>Regent of the Regex; Master of Marked</small>
      </td>
      <td align="center" valign="top">
        <a href="https://github.com/karenyavine">
          <img width="100" height="100" src="https://github.com/karenyavine.png?s=150">
        </a>
        <br>
        <a href="https://github.com/karenyavine">Karen Yavine</a>
        <div>Contributor</div>
        <small>Snyk's Security Saint</small>
      </td>
     </tr>
     <tr>
      <td align="center" valign="top">
        <a href="https://github.com/KostyaTretyak">
          <img width="100" height="100" src="https://github.com/KostyaTretyak.png?s=150">
        </a>
        <br>
        <a href="https://github.com/KostyaTretyak">Костя Третяк</a>
        <div>Contributor</div>
        <small></small>
      </td>
      <td align="center" width="20%" valign="top">
        <a href="https://github.com/tomtheisen">
          <img width="100" height="100" src="https://github.com/tomtheisen.png?s=150">
        </a>
        <br>
        <a href="https://github.com/tomtheisen">Tom Theisen</a>
        <div>Contributor</div>
        <small>Defibrillator</small>
      </td>
      <td align="center" width="20%" valign="top">
        <a href="https://github.com/mccraveiro">
          <img width="100" height="100" src="https://github.com/mccraveiro.png?s=150">
        </a>
        <br>
        <a href="https://github.com/mccraveiro">Mateus Craveiro</a>
        <div>Contributor</div>
        <small>Defibrillator</small>
      </td>
      <td align="center" width="20%" valign="top">
      </td>
     </tr>
  </tbody>
</table>

## Publishers

Publishers are admins who also have the responsibility, privilege, and burden of publishing the new releases to NPM and performing outreach and external stakeholder communications. Further, when things go pear-shaped, they're the ones taking most of the heat. Finally, when things go well, they're the primary ones praising the contributors who made it possible.

(In other words, while Admins are focused primarily on the internal workings of the project, Publishers are focused on internal *and* external concerns.)

**Should not exceed 2:** Having more people with the authority to publish a release can quickly turn into a consensus seeking nightmare (design by committee). Having only one is preferred (Directly Responsible Individual); however, given the nature of the project and its history, having an immediate fallback, and a potential deep fallback (Original author) is probably a good idea.

[Details on badges](#badges)

## Admins

Admins are committers who also have the responsibility, privilege, and burden of selecting committers and making sure the project itself runs smoothly, which includes community maintenance, governance, dispute resolution, and so on. (Letting the contributors easily enter into, and work within, the project to begin contributing, with as little friction as possible.)

**Should not exceed 3:** When there are too many people with the ability to resolve disputes, the dispute itself can quickly turn into a dispute amongst the admins themselves; therefore, we want this group to be small enough to commit to action and large enough to not put too much burden on one person. (Should ensure faster resolution and responsiveness.)

To be listed: Admins are usually selected from the pool of committers (or they volunteer, using the same process) who demonstrate good understanding of the marked culture, operations, and do their best to help new contributors get up to speed on how to contribute effectively to the project.

To be removed: You can remove yourself through the [GitHub UI](https://help.github.com/articles/removing-yourself-from-a-collaborator-s-repository/).

[Details on badges](#badges)

## Committers

Committers are contributors who also have the responsibility, privilege, some might even say burden of being able to review and merge contributions (just usually not their own).

A note on "decision making authority". This is related to submitting PRs and the [advice process](https://reinventingorganizationswiki.com/en/theory/decision-making/). The person marked as having decision making authority over a certain area should be sought for advice in that area before committing to a course of action.

**Should not exceed 5:** For larger PRs affecting more of the codebase and, most likely, review by more people, we try to keep this pool small and responsive and let those with decision making authority have final say without negative repercussions from the other committers.

To be listed: Committers are usually selected (or they volunteer, using the same process) from contributors who enter the discussions regarding the future direction of Marked (maybe even doing informal reviews of contributions despite not being able to merge them yourself).

To be removed: You can remove yourself through the [GitHub UI](https://help.github.com/articles/removing-yourself-from-a-collaborator-s-repository/).

A note on volunteering:

1. Please do not volunteer unless you believe you can demonstrate to your peers you can do the work required.
2. Please do not overcommit yourself; we count on those committed to the project to be responsive. Really consider, with all you have going on, whether you able to really commit to it.
3. Don't let the previous frighten you away, it can always be changed later by you or your peers.

[Details on badges](#badges)

## Contributors

Contributors are users who submit a [PR](https://github.com/markedjs/marked/pulls), [Issue](https://github.com/markedjs/marked/issues), or collaborate in making Marked a better product and experience for all the users.

To be listed: make a contribution and, if it has significant impact, the committers may be able to add you here.

To be removed: please let us know or submit a PR.

[Details on badges](#badges)

## Users

Users are anyone using Marked in some fashion, without them, there's no reason for us to exist.

|Individual or Organization |Website                 |Project                              |Submitted by                                        |
|:--------------------------|:-----------------------|:------------------------------------|:---------------------------------------------------|
|MarkedJS                   |https://marked.js.org   |https://github.com/markedjs/marked   |The marked committers                               |

To be listed: All fields are optional. Contact any of the committers or, more timely, submit a pull request with the following (using the first row as an example):

- **Individual or Organization:** The name you would like associated with the record.
- **Website:** A URL to a standalone website for the project.
- **Project:** A URL for the repository of the project using marked.
- **Submitted by:** The name and optional honorifics for the person adding the listing.

To be removed: Same as above. Only instead of requesting addition request deletion or delete the row yourself.

<h2 id="badges">Badges</h2>

Badges? You don't *need* no stinkin' badges.

Movie references aside. (It was either that or, "Let's play a game", but that would have been creepy&hellip;that's why it will most likely come later.)

Badges? If you *want* 'em, we got 'em, and here's how you get 'em (and&hellip;dramatic pause&hellip;why not two dramatic pauses for emphasis?&hellip; how they can be taken away).

- [ ] Add the appropriate badge to the desired contributor in the desired column of this page, even if they're not listed here yet.
- [ ] Submit a PR (we're big on PRs around here, if you haven't noticed, help us help you).
- [ ] Follow the instructions for submitting a badge PR. (There are more details to find within. Come on. Everybody likes surprises, right? No? Actually, we just try to put documentation where it belongs, closer to the code and part of the sequence of events.)

### Badges at play:

<dl>
	<dt>Curious Contributor</dt>
	<dd>A contributor with less than one year on this page who is actively engaged in submitting PRs, Issues, making recommendations, sharing thoughts&hellip;without being too annoying about it (let's be clear, submitting 100 Issues recommending the Marked Committers send everyone candy is trying for the badge, not honestly earning it).</dd>
	<dt>Dr. DevOps</dt>
	<dd>
		<p>Someone who understands and contributes to improving the developer experience and flow of Marked into the world.</p>
		<blockquote>
			"The main characteristic of the DevOps movement is to strongly advocate automation and monitoring at all steps of software construction, from integration, testing, releasing to deployment and infrastructure management. DevOps aims at shorter development cycles, increased deployment frequency, more dependable releases, in close alignment with business objectives." ~ <a href="https://www.wikipedia.org/wiki/DevOps">Wikipedia</a>
		</blockquote>
	</dd>
  <dt>Dr. Docs</dt>
  <dd>Someone who has contributed a great deal to the creation and maintenance of the non-code areas of marked.</dd>
	<dt>Eye for the CLI</dt>
	<dd>At this point? Pretty much anyone who can update that `man` file to the current Marked version without regression in the CLI tool itself.</dd>
	<dt>GitHub Guru</dt>
	<dd>Someone who always seems to be able to tell you easier ways to do things with GitHub.</dd>
	<dt>Humaning Helper</dt>
	<dd>Someone who goes out of their way to help contributors feel welcomed and valued. Further, someone who takes the extra steps(s) necessary to help new contributors get up to speed. Finally, they maintain composure even in times of disagreement and dispute resolution.</dd>
	<dt>Heckler of Hypertext</dt>
	<dd>Someone who demonstrates an esoteric level of knowledge when it comes to HTML. In other words, someone who says things like, "Did you know most Markdown flavors don't have a way to render a description list (`dl`)? All the more reason Markdown `!==` HTML."</dd>
	<dt>Markdown Maestro</dt>
	<dd>You know that person who knows about way too many different flavors of Markdown? The one who maybe seems a little too obsessed with the possibilities of Markdown beyond HTML? Come on. You know who they are. Or, at least you could, if you give them this badge.</dd>
	<dt>Master of Marked</dt>
	<dd>Someone who demonstrates they know the ins and outs of the codebase for Marked.</dd>
	<dt>Open source, of course</dt>
	<dd>Someone who advocates for and has a proven understanding of how to operate within open source communities.</dd>
	<dt>Regent of the Regex</dt>
	<dd><p>Can you demonstrate you understand the following without Google and Stackoverflow?</p>
		<p><code>/^( *)(bull) [\s\S]+?(?:hr|def|\n{2,}(?! )(?!\1bull )\n*|\s*$)/</code></p>
		<p>Because this author can't yet. That's who gets these.</p>
	</dd>
	<dt>Seeker of Security</dt>
	<dd>Someone who has demonstrated a high degree of expertise or authority when it comes to software security.</dd>
	<dt>Titan of the Test Harness</dt>
	<dd>Someone who demonstrates high-levels of understanding regarding Marked's test harness.</dd>
	<dt>Totally Tron</dt>
	<dd>Someone who demonstrates they are willing and able to "fight for the users", both developers dependent on marked to do their jobs as well as end-users interacting with the output (particularly in the realm of those with the disabilities).</dd>
</dl>

### Special badges that come with the job:

<dl>
	<dt>Defibrillator</dt>
	<dd>A contributor who stepped up to help bring Marked back to life by contributing solutions to help Marked pass when compared against the CommonMark and GitHub Flavored Markdown specifications.</dd>
	<dt>Maker of the Marked mark</dt>
	<dd>This badge is given to the person or organization credited with creating the logo (or logotype) used in Marked communications for a given period of time. **Maker of the Marked mark from 2017 to present**, for example.</dd>
	<dt>Release Wrangler</dt>
	<dd>This is a badge given to all Publishers.</dd>
	<dt>Snyk's Security Saint</dt>
	<dd>This is a badge given to whomever primarily reaches out from Snyk to let us know about security issues.</dd>
</dl>
