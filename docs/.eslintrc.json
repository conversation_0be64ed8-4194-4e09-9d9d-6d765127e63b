{"extends": "standard", "parserOptions": {"ecmaVersion": 2015, "sourceType": "script"}, "rules": {"semi": ["error", "always"], "indent": ["error", 2, {"SwitchCase": 1, "VariableDeclarator": {"var": 2}, "outerIIFEBody": 0}], "operator-linebreak": ["error", "before", {"overrides": {"=": "after"}}], "space-before-function-paren": ["error", "never"], "no-cond-assign": "off", "no-useless-escape": "off", "one-var": "off", "no-control-regex": "off", "no-prototype-builtins": "off", "prefer-const": "error", "no-var": "error"}, "env": {"node": true, "browser": true, "amd": true}}