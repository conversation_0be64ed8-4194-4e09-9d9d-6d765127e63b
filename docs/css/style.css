body {
    font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";
    font-size: 16px;
    line-height: 1.5;
    word-wrap: break-word;
    background: #F9F9F9;
}

#container {
    max-width: 900px;
    margin: auto;
}

#content {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background: white;
}

#content h1:first-child {
    margin-top: 0px;
}

nav {
    border: 1px solid #ddd;
    border-radius: 3px;
    background: white;
    margin-right: 10px;
}

nav > ul {
    position: sticky;
    top: 5px;
    margin: 10px 0px;
    padding: 0;
    list-style-type: none;
    font-size: 14px;
}

nav > ul > li {
    min-width: 125px;
    padding: 0px 15px;
}

nav > ul > li > ul {
    padding-left: 25px;
}

nav > ul > li > ul > li {
    font-size: 0.8em;
}

nav .selected {
    color: #111;
    font-weight: bold;
}

nav .selected:hover {
    text-decoration: none;
}

header {
    display: flex;
}

header h1 { margin: 0; }

table {
    border-spacing: 0;
    border-collapse: collapse;
    border: 1px solid #ddd;
}

td, th {
    border: 1px solid #ddd;
    padding: 5px;
}

a {
    color: #0366d6;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

pre {
    font-family: "SFMono-Regular",Consolas,"Liberation Mono",Menlo,Courier,monospace;
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f6f8fa;
    border-radius: 3px;
    position: relative;
    max-width: 680px;
}

pre code {
    max-width: 680px;
    overflow: auto;
    display: block;
}

:not(pre) > code {
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: #f6f8fa;
    border-radius: 3px;
}

summary {
    cursor: pointer;
    padding: 3px 0px;
}

summary:hover {
    color: #0366d6;
}

.div-copy {
    position: absolute;
    top: 0;
    right: 0;
}

.div-copy .icon-copy {
    opacity: 0;
    transition: opacity .3s;
    height: 18px;
    width: 18px;
    cursor: pointer;
    padding: 5px;
}

.div-copy.active .icon-copy {
    opacity: 1;
}

.div-copy .tooltip-copy {
    position: relative;
}

.div-copy .tooltip-copy::before {
    content: "Copied";
    position: absolute;

    /* vertically center */
    top: 50%;
    transform: translateY(-50%);

    /* move to right */
    right: 100%;
    margin-right: 5px; /* and add a small left margin */

    /* basic styles */
    padding: 2px 7px;
    border-radius: 5px;
    background: #444;
    color: #fff;
    text-align: center;

    opacity: 0; /* hide by default */
    transition: opacity .3s;
}

.div-copy.click .tooltip-copy::before {
    opacity: 1;
}

.div-copy .tooltip-copy::after {
    content: "";
    position: absolute;

    /* position tooltip correctly */
    right: 100%;
    margin-right: -5px;

    /* vertically center */
    top: 50%;
    transform: translateY(-50%);

    /* the arrow */
    border-style: solid;
    border-width: 2px 2px 5px 8px;
    border-color: transparent transparent transparent #444;

    opacity: 0;
    transition: opacity .3s;
}

.div-copy.click .tooltip-copy::after {
    opacity: 1;
}
