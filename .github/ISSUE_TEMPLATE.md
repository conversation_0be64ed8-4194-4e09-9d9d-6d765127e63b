**Marked version:**

**Markdown flavor:** Markdown.pl|CommonMark|GitHub Flavored Markdown|n/a

<!-- The NPM version or commit hash having the issue -->

<!--

	If submitting something other than a defect with Mark<PERSON> itself, please use the following:

**Proposal type:** new feature | project operations | other

## What pain point are you perceiving?

## What solution are you suggesting?

-->

## Expectation

**CommonMark Demo:** [demo](https://spec.commonmark.org/dingus/)
<!-- You can replace the link above with a permalink from: https://spec.commonmark.org/dingus/ -->

<!-- Describe the output you are expecting from marked -->

## Result

**Marked Demo:** [demo](https://marked.js.org/demo/)
<!-- You can replace the link above with a permalink from: https://marked.js.org/demo/ -->

<!-- Describe the output you received from marked -->

## What was attempted

<!-- Describe what code combination got you there -->

<!--
	If error is thrown add the following:

## Call stack & console log

-->
