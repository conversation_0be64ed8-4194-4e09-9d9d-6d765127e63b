---
name: Bug report
about: <PERSON><PERSON> says it does this thing but does not

---
**Marked version:**

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:

<!-- If possible, use the Marked Demo permalink and compare to the CommonMark Dingus permalink to demonstrate the bug -->
<!--
1. [Marked Demo](https://marked.js.org/demo/)
2. [CommonMark Demo](https://spec.commonmark.org/dingus/)
-->

<!-- If you need a specific version and options to reproduce the bug, use the following template -->
<!--
1. Install marked `npm install --save marked@0.3.19` with the version you are using
2. Run marked with input string and options such as `marked('hello *world*', {gfm: true})`
3. Actual output (or error) is...
-->

**Expected behavior**
A clear and concise description of what you expected to happen.
