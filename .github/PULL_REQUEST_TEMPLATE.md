
<!--

	If badging PR, add ?template=badges.md to the PR url to use the badges PR template.

	Otherwise, you are stating this PR fixes an issue that has been submitted; or,
	describes the issue or proposal under consideration and contains the project-related code to implement.

-->

**Marked version:**

<!-- The NPM version or commit hash having the issue -->

**Markdown flavor:** Markdown.pl|CommonMark|GitHub Flavored Markdown|n/a

## Description

- Fixes #### (if fixing a known issue; otherwise, describe issue using the following format)

<!--

	If no issue exists that you're aware of. The maintainers should be able to figure out if it's a duplicate.

## Expectation

Describe the output you are expecting from marked

## Result

Describe the output you received from marked

## What was attempted

Describe what code combination got you there

-->

## Contributor

- [ ] Test(s) exist to ensure functionality and minimize regression (if no tests added, list tests covering this PR); or,
- [ ] no tests required for this PR.
- [ ] If submitting new feature, it has been documented in the appropriate places.

## Committer

In most cases, this should be a different person than the contributor.

- [ ] CI is green (no forced merge required).
- [ ] Squash and Merge PR following [conventional commit guidelines](https://www.conventionalcommits.org/).
