<ul>
<li>伪类：<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:active">:active</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:any-link">:any-link</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:blank">:blank</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:checked">:checked</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:current">:current</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:default">:default</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:defined">:defined</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:dir">:dir()</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:disabled">:disabled</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:drop">:drop</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:empty">:empty</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:enabled">:enabled</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:first">:first</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:first-child">:first-child</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:first-of-type">:first-of-type</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:fullscreen">:fullscreen</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:future">:future</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:focus">:focus</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:focus-visible">:focus-visible</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:focus-within">:focus-within</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:has">:has()</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:host">:host</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:host()">:host()</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:host-context()">:host-context()</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:hover">:hover</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:indeterminate">:indeterminate</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:in-range">:in-range</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:invalid">:invalid</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:is">:is()</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:lang">:lang()</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:last-child">:last-child</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:last-of-type">:last-of-type</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:left">:left</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:link">:link</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:local-link">:local-link</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:not">:not()</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:nth-child">:nth-child()</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:nth-col">:nth-col()</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:nth-last-child">:nth-last-child()</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:nth-last-col">:nth-last-col()</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:nth-last-of-type">:nth-last-of-type()</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:nth-of-type">:nth-of-type()</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:only-child">:only-child</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:only-of-type">:only-of-type</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:optional">:optional</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:out-of-range">:out-of-range</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:past">:past</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:placeholder-shown">:placeholder-shown</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:read-only">:read-only</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:read-write">:read-write</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:required">:required</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:right">:right</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:root">:root</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:scope">:scope</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:target">:target</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:target-within">:target-within</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:user-invalid">:user-invalid</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:valid">:valid</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:visited">:visited</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/:where">:where()</a></li>
<li>伪元素：<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/::after">::after (:after)</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/::backdrop">::backdrop</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/::before">::before (:before)</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/::cue">::cue (:cue)</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/::first-letter">::first-letter (:first-letter)</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/::first-line">::first-line (:first-line)</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/::grammar-error">::grammar-error</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/::marker">::marker</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/::placeholder">::placeholder</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/::selection">::selection</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/::slotted">::slotted()</a>、<a href="https://developer.mozilla.org/en-US/docs/Web/CSS/::spelling-error">::spelling-error</a> </li>
</ul>

