<h3>Example 1</h3>

<!-- comment -->

<h3>Example 2</h3>

<!---->

<h3>Example 3</h3>

<!-- -->

<h3>Example 4</h3>

<!-- - -->

<h3>Example 5</h3>

<!-- -- -->

<h3>Example 6</h3>

<!-- --->

<h3>Example 7</h3>

<!----->

<h3>Example 8</h3>

<!------>

<h3>Example 9</h3>

<!-- My favorite operators are > and <!-->

<h3>Example 10</h3>

<!-- multi
line
comment
-->

<h3>Example 11</h3>

   <!-- indented comment -->

<pre><code>&lt;!-- too much indentation --&gt;
</code></pre>

<h3>Example 12</h3>

<!--> a comment --&gt;

<!---> a comment --&gt;

<!-- <!-- a comment? --> -->

<h3>Example 13</h3>

<!-- block ends at the end of the document since --!>

*is not a valid comment ending*
