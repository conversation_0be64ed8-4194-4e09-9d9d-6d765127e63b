<p>dash_capstyle: [&#39;butt&#39; | &#39;round&#39; | &#39;projecting&#39;]
dash_joinstyle: [&#39;miter&#39; | &#39;round&#39; | &#39;bevel&#39;]
dashes: sequence of on/off ink in points
drawstyle: [&#39;default&#39; | &#39;steps&#39; | &#39;steps-pre&#39; | &#39;steps-mid&#39; | &#39;steps-post&#39;]
figure: a <code>~.Figure</code> instance
fillstyle: [&#39;full&#39; | &#39;left&#39; | &#39;right&#39; | &#39;bottom&#39; | &#39;top&#39; | &#39;none&#39;]
gid: an id string
label: object
linestyle or ls: [&#39;solid&#39; | &#39;dashed&#39;, &#39;dashdot&#39;, &#39;dotted&#39; | (offset, on-off-dash-seq) | <code>&#39;-&#39;</code> | <code>&#39;--&#39;</code> | <code>&#39;-.&#39;</code> | <code>&#39;:&#39;</code> | <code>&#39;None&#39;</code> | <code>&#39; &#39;</code> | <code>&#39;&#39;</code>]
linewidth or lw: float value in points
marker: :mod:<code>A valid marker style &lt;matplotlib.markers&gt;</code>
markeredgecolor or mec: any matplotlib color
markeredgewidth or mew: float value in points
markerfacecolor or mfc: any matplotlib color
markerfacecoloralt or mfcalt: any matplotlib color
markersize or ms: float
markevery: [None | int | length-2 tuple of int | slice | list/array of int | float | length-2 tuple of float]
path_effects: <code>~.AbstractPathEffect</code>
picker: float distance in points or callable pick function <code>fn(artist, event)</code>
pickradius: float distance in points
rasterized: bool or None
sketch_params: (scale: float, length: float, randomness: float)
snap: bool or None
solid_capstyle: [&#39;butt&#39; | &#39;round&#39; |  &#39;projecting&#39;]
solid_joinstyle: [&#39;miter&#39; | &#39;round&#39; | &#39;bevel&#39;]
transform: a :class:<code>matplotlib.transforms.Transform</code> instance
url: a url string
visible: bool
xdata: 1D array
ydata: 1D array
zorder: float</p>
