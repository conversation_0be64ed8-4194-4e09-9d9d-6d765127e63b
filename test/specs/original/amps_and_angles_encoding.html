<p>AT&amp;T has an ampersand in their name.</p>

<p>AT&amp;T is another way to write it.</p>

<p>This &amp; that.</p>

<p>4 &lt; 5.</p>

<p>6 > 5.</p>

<p>Here's a <a href="http://example.com/?foo=1&amp;bar=2">link</a> with an ampersand in the URL.</p>

<p>Here's a link with an amersand in the link text: <a href="http://att.com/" title="AT&amp;T">AT&amp;T</a>.</p>

<p>Here's an inline <a href="/script?foo=1&amp;bar=2">link</a>.</p>

<p>Here's an inline <a href="/script?foo=1&amp;bar=2">link</a>.</p>
