<p>Here's a simple block:</p>

<div>
    foo
</div>

<p>This should be a code block, though:</p>

<pre><code>&lt;div&gt;
    foo
&lt;/div&gt;
</code></pre>

<p>As should this:</p>

<pre><code>&lt;div&gt;foo&lt;/div&gt;
</code></pre>

<p>Now, nested:</p>

<div>
    <div>
        <div>
            foo
        </div>
    </div>
</div>

<p>This should just be an HTML comment:</p>

<!-- Comment -->

<p>Multiline:</p>

<!--
Blah
Blah
-->

<p>Code block:</p>

<pre><code>&lt;!-- Comment --&gt;
</code></pre>

<p>Just plain comment, with trailing spaces on the line:</p>

<!-- foo -->   

<p>Code:</p>

<pre><code>&lt;hr /&gt;
</code></pre>

<p>Hr's:</p>

<hr>

<hr/>

<hr />

<hr>   

<hr/>  

<hr /> 

<hr class="foo" id="bar" />

<hr class="foo" id="bar"/>

<hr class="foo" id="bar" >
