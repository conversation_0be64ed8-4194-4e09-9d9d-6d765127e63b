import * as esbuild from 'esbuild';
import { umdWrapper } from 'esbuild-plugin-umd-wrapper';
import fs from 'fs';

const version = process.env.SEMANTIC_RELEASE_NEXT_VERSION || JSON.parse(fs.readFileSync('./package.json')).version;

console.log('building version:', version);

const banner = `/**
 * marked v${version} - a markdown parser
 * Copyright (c) 2011-${new Date().getFullYear()}, <PERSON>. (MIT Licensed)
 * https://github.com/markedjs/marked
 */

/**
 * DO NOT EDIT THIS FILE
 * The code in this file is generated from files in ./src/
 */
`;

function config(options) {
  return {
    entryPoints: ['src/marked.ts'],
    banner: {
      js: banner,
    },
    sourcemap: true,
    bundle: true,
    minify: true,
    ...(options.format === 'umd'
      ? {
        plugins: [umdWrapper({
          libraryName: 'marked',
        })],
      }
      : {}),
    ...options,
  };
}

await esbuild.build(config({
  format: 'esm',
  outfile: 'lib/marked.esm.js',
}));

await esbuild.build(config({
  format: 'umd',
  outfile: 'lib/marked.umd.js',
}));
